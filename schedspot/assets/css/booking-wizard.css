/**
 * SchedSpot Booking Wizard Styles
 * Modern, step-by-step booking interface
 */

/* Booking Wizard Container */
.schedspot-booking-wizard {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    border-radius: 16px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    position: relative;
}

/* Progress Bar */
.booking-progress {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px 30px;
    color: white;
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.progress-title {
    font-size: 24px;
    font-weight: 600;
    margin: 0;
}

.progress-step-info {
    font-size: 14px;
    opacity: 0.9;
}

.progress-bar {
    display: flex;
    align-items: center;
    gap: 8px;
}

.progress-step {
    flex: 1;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    transition: all 0.3s ease;
    position: relative;
}

.progress-step.completed {
    background: rgba(255, 255, 255, 0.9);
}

.progress-step.active {
    background: rgba(255, 255, 255, 0.7);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 1; }
}

/* Step Content */
.booking-steps {
    position: relative;
    min-height: 500px;
}

.booking-step {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 40px;
    opacity: 0;
    visibility: hidden;
    transform: translateX(30px);
    transition: all 0.4s ease;
}

.booking-step.active {
    opacity: 1;
    visibility: visible;
    transform: translateX(0);
    position: relative;
}

.booking-step.prev {
    transform: translateX(-30px);
}

.step-header {
    text-align: center;
    margin-bottom: 30px;
}

.step-title {
    font-size: 28px;
    font-weight: 700;
    color: #2c3e50;
    margin: 0 0 8px 0;
}

.step-subtitle {
    font-size: 16px;
    color: #6c757d;
    margin: 0;
}

.step-content {
    margin-bottom: 30px;
}

/* Service Selection */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.service-card {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 24px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    background: white;
}

.service-card:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.service-card.selected {
    border-color: #667eea;
    background: linear-gradient(135deg, #f8f9ff 0%, #e8ecff 100%);
}

.service-card.selected::before {
    content: '✓';
    position: absolute;
    top: 12px;
    right: 12px;
    width: 24px;
    height: 24px;
    background: #667eea;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: bold;
}

.service-name {
    font-size: 20px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 8px 0;
}

.service-description {
    font-size: 14px;
    color: #6c757d;
    margin: 0 0 16px 0;
    line-height: 1.5;
}

.service-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.service-duration {
    font-size: 14px;
    color: #6c757d;
}

.service-price {
    font-size: 18px;
    font-weight: 600;
    color: #667eea;
}

/* Worker Selection */
.workers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.worker-card {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.worker-card:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.worker-card.selected {
    border-color: #667eea;
    background: linear-gradient(135deg, #f8f9ff 0%, #e8ecff 100%);
}

.worker-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin: 0 auto 12px auto;
    overflow: hidden;
    border: 3px solid #e9ecef;
}

.worker-card.selected .worker-avatar {
    border-color: #667eea;
}

.worker-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.worker-name {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 4px 0;
}

.worker-rating {
    font-size: 14px;
    color: #f39c12;
    margin: 0 0 8px 0;
}

.worker-rate {
    font-size: 14px;
    color: #667eea;
    font-weight: 500;
}

/* Date & Time Selection */
.datetime-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.date-picker-section,
.time-picker-section {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 24px;
}

.section-title {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 16px 0;
}

.date-picker {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.3s ease;
}

.date-picker:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.time-slots {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 8px;
}

.time-slot {
    padding: 10px 8px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 500;
    background: white;
}

.time-slot:hover {
    border-color: #667eea;
    background: #f8f9ff;
}

.time-slot.selected {
    border-color: #667eea;
    background: #667eea;
    color: white;
}

.time-slot.unavailable {
    background: #f8f9fa;
    color: #6c757d;
    cursor: not-allowed;
    opacity: 0.6;
}

/* Contact Information */
.contact-form {
    display: grid;
    gap: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-label {
    font-size: 14px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
}

.form-input,
.form-textarea {
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.3s ease;
    font-family: inherit;
}

.form-input:focus,
.form-textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

/* Confirmation */
.booking-summary {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 30px;
}

.summary-title {
    font-size: 20px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 20px 0;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #e9ecef;
}

.summary-item:last-child {
    border-bottom: none;
    font-weight: 600;
    font-size: 18px;
    color: #667eea;
}

.summary-label {
    color: #6c757d;
}

.summary-value {
    font-weight: 500;
    color: #2c3e50;
}

/* Navigation Buttons */
.step-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30px 40px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.nav-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.nav-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.nav-btn-secondary {
    background: #6c757d;
    color: white;
}

.nav-btn-secondary:hover:not(:disabled) {
    background: #5a6268;
    transform: translateY(-1px);
}

.nav-btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.nav-btn-primary:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

/* Loading States */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .schedspot-booking-wizard {
        margin: 0 16px;
        border-radius: 12px;
    }
    
    .booking-step {
        padding: 24px;
    }
    
    .step-navigation {
        padding: 20px 24px;
    }
    
    .services-grid,
    .workers-grid {
        grid-template-columns: 1fr;
    }
    
    .datetime-container {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .form-row {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .step-title {
        font-size: 24px;
    }
    
    .progress-header {
        flex-direction: column;
        gap: 12px;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .booking-step {
        padding: 20px;
    }
    
    .step-navigation {
        padding: 16px 20px;
    }
    
    .nav-btn {
        padding: 10px 16px;
        font-size: 14px;
    }
    
    .time-slots {
        grid-template-columns: repeat(3, 1fr);
    }
}
