/**
 * SchedSpot Messaging System Styles
 *
 * @package SchedSpot
 * @version 1.0.0
 */

/* Main Messaging Container */
.schedspot-messaging {
    display: flex;
    height: 600px;
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    max-width: 1200px;
    margin: 0 auto;
}

.schedspot-messages-content {
    display: flex;
    height: 600px;
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
}

/* Conversations Sidebar */
.schedspot-conversations {
    width: 300px;
    border-right: 1px solid #ddd;
    background: #f9f9f9;
    display: flex;
    flex-direction: column;
}

.schedspot-conversations-header {
    padding: 20px;
    border-bottom: 1px solid #ddd;
    background: white;
}

.schedspot-conversations-header h3 {
    margin: 0;
    color: #333;
    font-size: 18px;
}

.schedspot-conversations-list {
    flex: 1;
    overflow-y: auto;
}

/* Conversation Items */
.schedspot-conversation-item {
    display: flex;
    padding: 15px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    transition: background-color 0.2s;
}

.schedspot-conversation-item:hover {
    background: #f0f0f0;
}

.schedspot-conversation-item.active {
    background: #0073aa;
    color: white;
}

.schedspot-conversation-item .user-avatar {
    margin-right: 10px;
}

.schedspot-conversation-item .user-avatar img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

.schedspot-conversation-item .conversation-info {
    flex: 1;
    min-width: 0;
}

.schedspot-conversation-item .user-name {
    font-weight: bold;
    margin-bottom: 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.schedspot-conversation-item .unread-count {
    background: #e74c3c;
    color: white;
    border-radius: 10px;
    padding: 2px 6px;
    font-size: 10px;
    min-width: 16px;
}

.schedspot-conversation-item .last-message {
    font-size: 12px;
    color: #666;
    margin-bottom: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.schedspot-conversation-item.active .last-message {
    color: rgba(255, 255, 255, 0.8);
}

.schedspot-conversation-item .time-ago {
    font-size: 11px;
    color: #999;
}

.schedspot-conversation-item.active .time-ago {
    color: rgba(255, 255, 255, 0.7);
}

/* Chat Area */
.schedspot-chat-area {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.schedspot-chat-header {
    padding: 20px;
    border-bottom: 1px solid #ddd;
    background: white;
    display: flex;
    align-items: center;
}

.schedspot-chat-header h3 {
    margin: 0;
    color: #333;
}

/* Messages Area */
.schedspot-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: #fafafa;
}

.schedspot-message {
    display: flex;
    margin-bottom: 15px;
    align-items: flex-start;
}

.schedspot-message.own {
    flex-direction: row-reverse;
}

.schedspot-message .avatar {
    margin: 0 10px;
}

.schedspot-message .avatar img {
    width: 32px;
    height: 32px;
    border-radius: 50%;
}

.schedspot-message .content {
    max-width: 70%;
    background: white;
    padding: 10px 15px;
    border-radius: 18px;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.schedspot-message.own .content {
    background: #0073aa;
    color: white;
}

.schedspot-message .text {
    margin-bottom: 5px;
}

.schedspot-message .time {
    font-size: 11px;
    color: #999;
}

.schedspot-message.own .time {
    color: rgba(255, 255, 255, 0.7);
}

/* Attachments */
.schedspot-attachment {
    display: inline-block;
    padding: 5px 10px;
    background: rgba(0,0,0,0.1);
    border-radius: 10px;
    margin-top: 5px;
    text-decoration: none;
    color: inherit;
    font-size: 12px;
}

.schedspot-attachment:hover {
    background: rgba(0,0,0,0.2);
}

/* Message Form */
.schedspot-message-form {
    padding: 20px;
    border-top: 1px solid #ddd;
    background: white;
}

.schedspot-message-input {
    display: flex;
    gap: 10px;
    align-items: flex-end;
}

.schedspot-message-input textarea {
    flex: 1;
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 20px;
    resize: none;
    min-height: 40px;
    max-height: 120px;
    font-family: inherit;
    font-size: 14px;
}

.schedspot-message-input textarea:focus {
    outline: none;
    border-color: #0073aa;
    box-shadow: 0 0 0 2px rgba(0, 115, 170, 0.2);
}

.schedspot-message-input .send-button {
    background: #0073aa;
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.schedspot-message-input .send-button:hover {
    background: #005a87;
}

.schedspot-message-input .send-button:disabled {
    background: #ccc;
    cursor: not-allowed;
}

/* File Upload */
.schedspot-file-upload {
    position: relative;
    overflow: hidden;
    display: inline-block;
}

.schedspot-file-upload input[type="file"] {
    position: absolute;
    left: -9999px;
}

.schedspot-file-upload label {
    background: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.schedspot-file-upload label:hover {
    background: #e9ecef;
}

/* Notifications */
.schedspot-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 4px;
    color: white;
    font-weight: 500;
    z-index: 9999;
    animation: slideInRight 0.3s ease-out;
}

.schedspot-notification.success {
    background: #28a745;
}

.schedspot-notification.error {
    background: #dc3545;
}

.schedspot-notification.info {
    background: #17a2b8;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .schedspot-messages-content {
        flex-direction: column;
        height: auto;
    }

    .schedspot-conversations {
        width: 100%;
        height: 200px;
    }

    .schedspot-chat-area {
        height: 400px;
    }

    .schedspot-message .content {
        max-width: 85%;
    }

    .schedspot-message-input {
        flex-wrap: wrap;
    }

    .schedspot-message-input textarea {
        min-height: 35px;
    }

    .schedspot-notification {
        left: 20px;
        right: 20px;
        top: 10px;
    }
}
