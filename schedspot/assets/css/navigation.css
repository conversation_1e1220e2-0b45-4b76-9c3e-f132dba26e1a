/**
 * SchedSpot Navigation Styles
 * Modern, responsive navigation system
 */

/* Navigation Container */
.schedspot-navigation {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 9999;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.schedspot-nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 60px;
}

/* Brand */
.schedspot-nav-brand {
    display: flex;
    align-items: center;
    gap: 10px;
    color: white;
    font-weight: 600;
    font-size: 18px;
    text-decoration: none;
}

.nav-logo {
    font-size: 24px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.nav-title {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
    letter-spacing: -0.5px;
}

/* Menu Toggle */
.nav-menu-toggle {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 8px 16px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 500;
}

.nav-menu-toggle:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.nav-menu-toggle.active {
    background: rgba(255, 255, 255, 0.25);
}

/* Hamburger Icon */
.hamburger-line {
    width: 18px;
    height: 2px;
    background: white;
    border-radius: 1px;
    transition: all 0.3s ease;
    display: block;
}

.nav-menu-toggle.active .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.nav-menu-toggle.active .hamburger-line:nth-child(2) {
    opacity: 0;
}

.nav-menu-toggle.active .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

/* Dropdown */
.nav-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    width: 320px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    margin-top: 8px;
    overflow: hidden;
}

.nav-dropdown.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* Dropdown Header */
.nav-dropdown-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
}

.nav-user-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.nav-user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.nav-user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.nav-user-details {
    flex: 1;
}

.nav-user-name {
    font-weight: 600;
    color: #2c3e50;
    font-size: 16px;
    margin-bottom: 2px;
}

.nav-user-role {
    font-size: 12px;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
}

.nav-guest-info h4 {
    margin: 0 0 4px 0;
    color: #2c3e50;
    font-size: 16px;
    font-weight: 600;
}

.nav-guest-info p {
    margin: 0;
    color: #6c757d;
    font-size: 14px;
}

/* Dropdown Content */
.nav-dropdown-content {
    max-height: 400px;
    overflow-y: auto;
    padding: 8px 0;
}

.nav-section {
    margin-bottom: 8px;
}

.nav-section:last-child {
    margin-bottom: 0;
}

.nav-section-title {
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: #6c757d;
    margin: 0;
    padding: 12px 20px 8px 20px;
}

.nav-section-items {
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-section-items li {
    margin: 0;
}

.nav-section-items a {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 20px;
    color: #2c3e50;
    text-decoration: none;
    transition: all 0.2s ease;
    position: relative;
}

.nav-section-items a:hover {
    background: #f8f9fa;
    color: #667eea;
    transform: translateX(4px);
}

.nav-icon {
    font-size: 16px;
    width: 20px;
    text-align: center;
    opacity: 0.8;
}

.nav-text {
    flex: 1;
    font-size: 14px;
    font-weight: 500;
}

.nav-badge {
    background: #dc3545;
    color: white;
    font-size: 10px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 16px;
    text-align: center;
    line-height: 1.2;
}

/* Dropdown Footer */
.nav-dropdown-footer {
    border-top: 1px solid #e9ecef;
    padding: 16px 20px;
    background: #f8f9fa;
}

.nav-logout-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #dc3545;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.nav-logout-btn:hover {
    color: #c82333;
    transform: translateX(2px);
}

.nav-auth-buttons {
    display: flex;
    gap: 8px;
}

.nav-login-btn,
.nav-register-btn {
    flex: 1;
    text-align: center;
    padding: 8px 16px;
    border-radius: 6px;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.nav-login-btn {
    background: #667eea;
    color: white;
}

.nav-login-btn:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
}

.nav-register-btn {
    background: transparent;
    color: #667eea;
    border: 1px solid #667eea;
}

.nav-register-btn:hover {
    background: #667eea;
    color: white;
    transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .schedspot-nav-container {
        padding: 0 16px;
        height: 56px;
    }
    
    .nav-dropdown {
        width: calc(100vw - 32px);
        right: 16px;
        left: 16px;
    }
    
    .nav-title {
        display: none;
    }
    
    .nav-menu-text {
        display: none;
    }
}

@media (max-width: 480px) {
    .nav-dropdown {
        width: calc(100vw - 16px);
        right: 8px;
        left: 8px;
    }
    
    .nav-dropdown-header,
    .nav-dropdown-footer {
        padding: 16px;
    }
    
    .nav-section-items a {
        padding: 10px 16px;
    }
}

/* Animation for smooth transitions */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.nav-dropdown.active {
    animation: slideDown 0.3s ease;
}

/* Scrollbar styling for dropdown content */
.nav-dropdown-content::-webkit-scrollbar {
    width: 4px;
}

.nav-dropdown-content::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.nav-dropdown-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
}

.nav-dropdown-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Ensure navigation doesn't interfere with page content */
body.schedspot-nav-active {
    padding-top: 60px;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .nav-dropdown {
        background: #2c3e50;
        color: white;
    }
    
    .nav-dropdown-header {
        background: #34495e;
        border-bottom-color: #4a5f7a;
    }
    
    .nav-user-name {
        color: white;
    }
    
    .nav-guest-info h4 {
        color: white;
    }
    
    .nav-section-items a {
        color: #ecf0f1;
    }
    
    .nav-section-items a:hover {
        background: #34495e;
        color: #3498db;
    }
    
    .nav-dropdown-footer {
        background: #34495e;
        border-top-color: #4a5f7a;
    }
}
